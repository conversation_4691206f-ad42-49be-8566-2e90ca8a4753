using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class Node : MonoBeh<PERSON>our
{
    public NodeData node;

    // Hàm khởi tạo custom (gọi sau Instantiate)
    public void Init(NodeData data)
    {
        node = data;
    }

    // private void OnMouseDown()
    // {
    //     if (node == null || node.state != NodeState.Normal || node.tileObject == null || !node.tileObject.activeSelf)
    //         return;
    //     BoardManager.Instance.SelectTile(node.posX, node.posY);
    // }
}
