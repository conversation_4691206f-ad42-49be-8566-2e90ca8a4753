using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class BoardManager : Singleton<BoardManager>
{

    [<PERSON><PERSON>("Data Manager")]
    public GameObject[] iconSprite;
    public int rows;
    public int cols;
    public float offsetX, offsetY;

    public NodeData[,] board;
    public GameObject[,] tiles;

    public List<NodeType> nodePool;

    // public List<NodeData> nodeData;


    [Header("In Game")]

    public NodeData firstSelect;



    private void Awake()
    {
        InitGrid();
        CreateBoard();
        firstSelect.state = NodeState.Empty;
        firstSelect.tileObject = null;
    }

    public Vector3 GetWorldPosition(int row, int col)
    {
        return new Vector3(
            col * offsetX + offsetX * 0.5f,   // X: theo cột, +0.5 để lấy tâm
            -row * offsetY - offsetY * 0.5f,  // Y: theo hàng, +0.5 để lấy tâm
            0
        );
    }


    private List<NodeType> GenerateNodePool()
    {
        List<NodeType> pool = new List<NodeType>();
        int repeat = 8;

        for (int i = 0; i < iconSprite.Length; i++)
        {
            for (int j = 0; j < repeat; j++)
            {
                pool.Add((NodeType)i);
            }
        }

        return pool;
    }

    // Fisher-Yates shuffle
    private void Shuffle<T>(List<T> list)
    {
        for (int i = list.Count - 1; i > 0; i--)
        {
            int randIndex = Random.Range(0, i + 1);
            T temp = list[i];
            list[i] = list[randIndex];
            list[randIndex] = temp;
        }
    }

    // Khởi tạo Grid
    private void InitGrid()
    {
        board = new NodeData[rows, cols];
        tiles = new GameObject[rows, cols];

        nodePool = GenerateNodePool();
        Shuffle(nodePool);

        int index = 0;

        for (int row = 0; row < rows; row++)
        {
            for (int col = 0; col < cols; col++)
            {
                NodeData node = new NodeData
                {
                    posX = row,
                    posY = col
                };

                if (row == 0 || row == rows - 1 || col == 0 || col == cols - 1)
                {
                    node.state = NodeState.Empty;
                    node.node = 0;
                }
                else
                {
                    node.state = NodeState.Normal;
                    node.node = nodePool[index];
                    index++;
                }
                // nodeData.Add(node);
                board[row, col] = node;
            }
        }
    }

    // Vẽ Board
    private void CreateBoard()
    {
        foreach (Transform child in transform)
        {
            DestroyImmediate(child.gameObject);
        }

        for (int row = 0; row < rows; row++)
        {
            for (int col = 0; col < cols; col++)
            {
                if (board[row, col].state == NodeState.Empty) continue;

                Vector3 pos = new Vector3(col * offsetX, -row * offsetY, 0);

                GameObject tile = Instantiate(iconSprite[(int)board[row, col].node], pos, Quaternion.identity, transform);
                tile.GetComponent<SpriteRenderer>().color = Color.white;
                Node nodeComp = tile.GetComponent<Node>();
                if (nodeComp != null)
                {
                    nodeComp.Init(board[row, col]);
                    nodeComp.node.tileObject = tile;
                    nodeComp.node.X = pos.x;
                    nodeComp.node.Y = pos.y;

                }
                tiles[row, col] = tile;
            }
        }
    }

    public void SelectTile(int row, int col)
    {
        NodeData node = board[row, col];
        if (node.state != NodeState.Normal) return;
        if (firstSelect.state != NodeState.Selected)
        {
            firstSelect = node;
            firstSelect.state = NodeState.Selected;
            firstSelect.tileObject.GetComponent<SpriteRenderer>().color = Color.yellow;
        }
        else
        {
            // Nếu có thể nối
            node.state = NodeState.Selected;
            node.tileObject.GetComponent<SpriteRenderer>().color = Color.yellow;
            if (PathFinder.Instance.CanConnect(board, rows, cols, firstSelect, node))
            {
                // Hide the path line after 1 second
                PathFinder.Instance.HideLineAfterDelay(1.0f);

                tiles[firstSelect.posX, firstSelect.posY].gameObject.SetActive(false);
                board[firstSelect.posX, firstSelect.posY].state = NodeState.Empty;
                tiles[row, col].gameObject.SetActive(false);
                board[row, col].state = NodeState.Empty;
                firstSelect.state = NodeState.Empty;
                node.state = NodeState.Empty;
                if (!HasAnyConnectablePair())
                {
                    ShuffleBoard();
                }
            }
            else
            {
                // reset màu
                firstSelect.tileObject.GetComponent<SpriteRenderer>().color = Color.white;
                node.tileObject.GetComponent<SpriteRenderer>().color = Color.white;
                firstSelect.state = NodeState.Normal;
                node.state = NodeState.Normal;
                board[row, col].state = NodeState.Normal;
            }

            firstSelect = null;
        }
    }

    public bool HasAnyConnectablePair()
    {
        // Duyệt từng node còn lại trên board
        for (int row1 = 0; row1 < rows; row1++)
        {
            for (int col1 = 0; col1 < cols; col1++)
            {
                NodeData nodeA = board[row1, col1];
                if (nodeA == null || nodeA.state != NodeState.Normal) continue;

                for (int row2 = row1; row2 < rows; row2++)
                {
                    int startCol = (row2 == row1) ? col1 + 1 : 0;
                    for (int col2 = startCol; col2 < cols; col2++)
                    {
                        NodeData nodeB = board[row2, col2];
                        if (nodeB == null || nodeB.state != NodeState.Normal) continue;
                        if (nodeA.node != nodeB.node) continue;

                        if (PathFinder.Instance.CanConnect(board, rows, cols, nodeA, nodeB))
                        {
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }

    public void ShuffleBoard()
    {
        List<NodeType> remainTypes = new List<NodeType>();
        for (int row = 0; row < rows; row++)
        {
            for (int col = 0; col < cols; col++)
            {
                if (board[row, col].state == NodeState.Normal)
                {
                    remainTypes.Add(board[row, col].node);
                }
            }
        }

        Shuffle(remainTypes);

        int idx = 0;
        for (int row = 0; row < rows; row++)
        {
            for (int col = 0; col < cols; col++)
            {
                if (board[row, col].state == NodeState.Normal)
                {
                    board[row, col].node = remainTypes[idx];
                    // Đổi sprite cho tile
                    tiles[row, col].GetComponent<SpriteRenderer>().sprite = iconSprite[(int)remainTypes[idx]].GetComponent<SpriteRenderer>().sprite;
                    idx++;
                }
            }
        }
    }
}

