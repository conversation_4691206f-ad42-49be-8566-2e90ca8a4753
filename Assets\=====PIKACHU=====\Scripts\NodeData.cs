using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;


[Serializable]
public class NodeData
{
    public float X, Y;
    public int posX;
    public int posY;
    public NodeType node;
    public NodeState state;
    public GameObject tileObject;

}



public enum NodeType
{
    Ani_0 = 0,
    Ani_1 = 1,
    Ani_2 = 2,
    An<PERSON>_3 = 3,
    Ani_4 = 4,
    Ani_5 = 5,
    <PERSON><PERSON>_6 = 6,
    <PERSON><PERSON>_7 = 7,
    <PERSON><PERSON>_8 = 8,
    <PERSON><PERSON>_9 = 9,
    <PERSON><PERSON>_10 = 10,
    <PERSON><PERSON>_11 = 11,
    <PERSON>i_12 = 12,
    <PERSON><PERSON>_13 = 13,
    <PERSON><PERSON>_14 = 14,
    <PERSON>i_15 = 15,
    Ani_16 = 16,
    <PERSON><PERSON>_17 = 17,
    <PERSON>i_18 = 18,
    <PERSON>i_19 = 19,
    <PERSON>i_20 = 20,
    <PERSON><PERSON>_21 = 21,
    <PERSON><PERSON>_22 = 22,
    <PERSON><PERSON>_23 = 23,
    <PERSON><PERSON>_24 = 24,
    <PERSON><PERSON>_25 = 25,
    <PERSON><PERSON>_26 = 26,
    <PERSON><PERSON>_27 = 27,
    <PERSON><PERSON>_28 = 28,
    <PERSON><PERSON>_29 = 29,
    <PERSON><PERSON>_30 = 30,
    <PERSON><PERSON>_31 = 31,
    <PERSON><PERSON>_32 = 32,
    <PERSON><PERSON>_33 = 33,
    <PERSON><PERSON>_34 = 34,
    <PERSON>i_35 = 35,
}



public enum NodeState
{
    Empty,
    Normal,
    Selected
}